/* CSS Variables for our color palette */
:root {
    --dark-metal: #2d3436;
    --dark-metal-rgb: 45, 52, 54;
    --silver-gray: #dfe6e9;
    --silver-light: #f1f2f6;
    --silver-medium: #c8d6e5;
    --fire-orange: #e17055;
    --light-text: #ffffff;
    --shadow-dark: rgba(0, 0, 0, 0.3);
    --shadow-light: rgba(255, 255, 255, 0.1);
}

/* Basic Reset & Body Styling */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Roboto', sans-serif;
    background-color: var(--dark-metal);
    color: var(--silver-gray);
    line-height: 1.6;
}

h1, h2, h3 {
    font-weight: 700;
    color: var(--light-text);
    margin-bottom: 20px;
}

section {
    padding: 80px 5%;
    text-align: center;
}

/* Header and Navigation */
.main-header {
    background: linear-gradient(135deg, var(--silver-light), var(--silver-gray));
    backdrop-filter: blur(10px);
    padding: 10px 5%;
    position: sticky;
    top: 0;
    z-index: 100;
    width: 100%;
    box-shadow: 0 4px 20px var(--shadow-dark);
    border-bottom: 2px solid var(--silver-medium);
}

.main-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    text-decoration: none;
    display: flex;
    align-items: center;
}

.logo-image {
    height: 80px;
    width: auto;
    filter: drop-shadow(2px 2px 4px var(--shadow-dark));
    transition: transform 0.3s ease;
}

.logo-image:hover {
    transform: scale(1.05);
}

.nav-links {
    list-style: none;
    display: flex;
}

.nav-links li {
    margin-left: 20px;
}

.nav-button {
    text-decoration: none;
    color: var(--dark-metal);
    font-weight: 700;
    padding: 12px 20px;
    background: linear-gradient(145deg, var(--silver-light), var(--silver-gray));
    border-radius: 8px;
    box-shadow:
        inset 2px 2px 5px var(--shadow-light),
        inset -2px -2px 5px var(--shadow-dark),
        2px 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.nav-button:hover {
    background: linear-gradient(145deg, var(--silver-gray), var(--silver-medium));
    box-shadow:
        inset 3px 3px 8px var(--shadow-dark),
        inset -1px -1px 3px var(--shadow-light);
    transform: translateY(1px);
    color: var(--fire-orange);
}

.nav-button:active {
    box-shadow:
        inset 4px 4px 10px var(--shadow-dark),
        inset -2px -2px 5px var(--shadow-light);
    transform: translateY(2px);
}

/* Hero Section */
.hero {
    position: relative;
    min-height: 90vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 60px 5%;
    background: linear-gradient(135deg, var(--silver-light) 0%, var(--silver-gray) 50%, var(--silver-medium) 100%);
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(225, 112, 85, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(45, 52, 54, 0.1) 0%, transparent 50%);
    z-index: 1;
}

.hero-content {
    z-index: 2;
    text-align: center;
    color: var(--dark-metal);
    max-width: 800px;
}

.hero-logo {
    margin-bottom: 40px;
    animation: logoFloat 3s ease-in-out infinite;
}

.hero-logo-image {
    height: 240px;
    width: auto;
    filter: drop-shadow(4px 4px 12px var(--shadow-dark));
}

.hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px var(--shadow-light);
    color: var(--dark-metal);
}

.hero-content p {
    font-size: 1.3rem;
    margin-bottom: 40px;
    color: var(--dark-metal);
    opacity: 0.8;
}

@keyframes logoFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Stamped Button Styles */
.stamped-button {
    background: linear-gradient(145deg, var(--fire-orange), #d35400);
    color: var(--light-text);
    padding: 18px 36px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    border-radius: 12px;
    box-shadow:
        inset 2px 2px 5px rgba(255, 255, 255, 0.2),
        inset -3px -3px 8px rgba(0, 0, 0, 0.3),
        4px 4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    display: inline-block;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.stamped-button:hover {
    background: linear-gradient(145deg, #d35400, var(--fire-orange));
    box-shadow:
        inset 4px 4px 10px rgba(0, 0, 0, 0.4),
        inset -2px -2px 5px rgba(255, 255, 255, 0.1),
        2px 2px 8px rgba(0, 0, 0, 0.3);
    transform: translateY(2px);
}

.stamped-button:active {
    box-shadow:
        inset 6px 6px 15px rgba(0, 0, 0, 0.5),
        inset -2px -2px 5px rgba(255, 255, 255, 0.1);
    transform: translateY(3px);
}

.cta-button {
    background: linear-gradient(145deg, var(--fire-orange), #d35400);
    color: var(--light-text);
    padding: 18px 36px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    border-radius: 12px;
    box-shadow:
        inset 2px 2px 5px rgba(255, 255, 255, 0.2),
        inset -3px -3px 8px rgba(0, 0, 0, 0.3),
        4px 4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    display: inline-block;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.cta-button:hover {
    background: linear-gradient(145deg, #d35400, var(--fire-orange));
    box-shadow:
        inset 4px 4px 10px rgba(0, 0, 0, 0.4),
        inset -2px -2px 5px rgba(255, 255, 255, 0.1),
        2px 2px 8px rgba(0, 0, 0, 0.3);
    transform: translateY(2px);
}

/* Services Section */
.services-section {
    background: linear-gradient(135deg, var(--dark-metal) 0%, #1e2426 100%);
    position: relative;
}

.services-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 40%, rgba(223, 230, 233, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(223, 230, 233, 0.03) 0%, transparent 50%);
    z-index: 1;
}

.services-section > * {
    position: relative;
    z-index: 2;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.service-card {
    background: linear-gradient(145deg, var(--silver-gray), var(--silver-light));
    border: 2px solid var(--silver-medium);
    padding: 40px 25px;
    border-radius: 15px;
    box-shadow:
        inset 2px 2px 5px rgba(255, 255, 255, 0.3),
        inset -2px -2px 5px rgba(0, 0, 0, 0.1),
        4px 4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(145deg, transparent, rgba(225, 112, 85, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.service-card:hover::before {
    opacity: 1;
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow:
        inset 3px 3px 8px rgba(0, 0, 0, 0.2),
        inset -1px -1px 3px rgba(255, 255, 255, 0.2),
        6px 6px 20px rgba(0, 0, 0, 0.3);
    background: linear-gradient(145deg, var(--silver-medium), var(--silver-gray));
}

.service-card h3 {
    color: var(--fire-orange);
    margin-bottom: 20px;
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.service-card p {
    color: var(--dark-metal);
    position: relative;
    z-index: 2;
    line-height: 1.6;
}

.service-card:hover h3 {
    color: var(--dark-metal);
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.3);
}

.service-card:hover p {
    color: var(--dark-metal);
}

/* Work Section */
.work-section {
    background: linear-gradient(135deg, #1e2426 0%, var(--dark-metal) 100%);
    position: relative;
}

.work-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 60% 30%, rgba(223, 230, 233, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 20% 70%, rgba(225, 112, 85, 0.05) 0%, transparent 50%);
    z-index: 1;
}

.work-section > * {
    position: relative;
    z-index: 2;
}

.work-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 25px;
    margin-top: 50px;
}

.work-item {
    border-radius: 15px;
    overflow: hidden;
    box-shadow:
        4px 4px 15px rgba(0, 0, 0, 0.3),
        inset 1px 1px 3px rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    background: linear-gradient(145deg, var(--silver-gray), var(--silver-light));
    padding: 3px;
}

.work-item:hover {
    transform: translateY(-5px);
    box-shadow:
        6px 6px 20px rgba(0, 0, 0, 0.4),
        inset 2px 2px 5px rgba(255, 255, 255, 0.1);
}

.work-item img {
    width: 100%;
    height: auto;
    display: block;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.work-item:hover img {
    transform: scale(1.02);
}

/* About Section */
.about-section {
    background: linear-gradient(135deg, var(--silver-light) 0%, var(--silver-gray) 100%);
    position: relative;
}

.about-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 40% 60%, rgba(45, 52, 54, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(225, 112, 85, 0.03) 0%, transparent 50%);
    z-index: 1;
}

.about-section > * {
    position: relative;
    z-index: 2;
}

.about-section h2 {
    color: var(--dark-metal);
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.3);
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(223, 230, 233, 0.2));
    padding: 40px;
    border-radius: 20px;
    box-shadow:
        inset 2px 2px 5px rgba(255, 255, 255, 0.3),
        inset -2px -2px 5px rgba(0, 0, 0, 0.1),
        4px 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--silver-medium);
}

.about-content p {
    color: var(--dark-metal);
    line-height: 1.8;
    font-size: 1.1rem;
}

/* Footer */
.main-footer {
    background: linear-gradient(135deg, #1e2426 0%, var(--dark-metal) 100%);
    text-align: center;
    padding: 50px 5%;
    position: relative;
}

.main-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 50% 50%, rgba(223, 230, 233, 0.03) 0%, transparent 50%);
    z-index: 1;
}

.main-footer > * {
    position: relative;
    z-index: 2;
}

.main-footer h3 {
    color: var(--fire-orange);
    font-size: 1.8rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    margin-bottom: 15px;
}

.footer-contact {
    margin: 30px 0;
    color: var(--silver-gray);
}

.footer-contact p {
    margin: 10px 0;
    font-size: 1.1rem;
}

.copyright {
    font-size: 0.9rem;
    color: var(--silver-medium);
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--silver-medium);
}


/* Responsive Design for Mobile */
@media (max-width: 768px) {
    .nav-links {
        display: none; /* For a real site, implement a hamburger menu here */
    }

    .logo-image {
        height: 60px;
    }

    .hero-logo-image {
        height: 180px;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-content p {
        font-size: 1.1rem;
    }

    .stamped-button, .cta-button {
        padding: 15px 25px;
        font-size: 1rem;
    }

    .nav-button {
        padding: 10px 15px;
        font-size: 0.9rem;
    }

    section {
        padding: 60px 5%;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .work-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .about-content {
        padding: 30px 20px;
    }
}