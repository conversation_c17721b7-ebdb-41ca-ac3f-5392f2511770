/* CSS Variables for our color palette */
:root {
    --dark-grey: #4a4a4a;
    --medium-grey: #6a6a6a;
    --light-grey: #8a8a8a;
    --very-light-grey: #b0b0b0;
    --fire-orange: #e17055;
    --fire-orange-light: #e8845f;
    --fire-orange-dark: #d35400;
    --light-text: #ffffff;
    --shadow-dark: rgba(0, 0, 0, 0.3);
    --shadow-light: rgba(255, 255, 255, 0.1);
}

/* Basic Reset & Body Styling */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Roboto', sans-serif;
    background-color: var(--dark-grey);
    color: var(--very-light-grey);
    line-height: 1.6;
}

h1, h2, h3 {
    font-weight: 700;
    color: var(--light-text);
    margin-bottom: 20px;
}

section {
    padding: 80px 5%;
    text-align: center;
}

/* Head<PERSON> and <PERSON> */
.main-header {
    background: linear-gradient(135deg, var(--medium-grey), var(--dark-grey));
    backdrop-filter: blur(10px);
    padding: 10px 5%;
    position: sticky;
    top: 0;
    z-index: 100;
    width: 100%;
    box-shadow: 0 4px 20px var(--shadow-dark);
    border-bottom: 2px solid var(--fire-orange);
}

.main-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    text-decoration: none;
    display: flex;
    align-items: center;
}

.logo-image {
    height: 80px;
    width: auto;
    filter: drop-shadow(2px 2px 4px var(--shadow-dark));
    transition: transform 0.3s ease;
}

.logo-image:hover {
    transform: scale(1.05);
}

.nav-links {
    list-style: none;
    display: flex;
}

.nav-links li {
    margin-left: 20px;
}

.nav-button {
    text-decoration: none;
    color: var(--light-text);
    font-weight: 700;
    padding: 12px 20px;
    background: linear-gradient(145deg, var(--light-grey), var(--medium-grey));
    border-radius: 8px;
    box-shadow:
        inset 2px 2px 5px var(--shadow-light),
        inset -2px -2px 5px var(--shadow-dark),
        2px 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.nav-button:hover {
    background: linear-gradient(145deg, var(--fire-orange), var(--fire-orange-dark));
    box-shadow:
        inset 3px 3px 8px var(--shadow-dark),
        inset -1px -1px 3px var(--shadow-light);
    transform: translateY(1px);
    color: var(--light-text);
}

.nav-button:active {
    box-shadow:
        inset 4px 4px 10px var(--shadow-dark),
        inset -2px -2px 5px var(--shadow-light);
    transform: translateY(2px);
}

/* Hero Section */
.hero {
    position: relative;
    min-height: 90vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 60px 5%;
    background:
        linear-gradient(135deg, rgba(74, 74, 74, 0.8) 0%, rgba(106, 106, 106, 0.6) 100%),
        url('logo.png') center center no-repeat;
    background-size: contain;
    background-blend-mode: overlay;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(225, 112, 85, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(74, 74, 74, 0.1) 0%, transparent 50%);
    z-index: 1;
}

.hero-content {
    z-index: 2;
    position: absolute;
    bottom: 60px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: var(--light-text);
}

.hero-content p {
    font-size: 1.4rem;
    margin-bottom: 30px;
    color: var(--light-text);
    text-shadow: 2px 2px 4px var(--shadow-dark);
    font-weight: 500;
}

/* Stamped Button Styles */
.stamped-button {
    background: linear-gradient(145deg, var(--fire-orange), #d35400);
    color: var(--light-text);
    padding: 18px 36px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    border-radius: 12px;
    box-shadow:
        inset 2px 2px 5px rgba(255, 255, 255, 0.2),
        inset -3px -3px 8px rgba(0, 0, 0, 0.3),
        4px 4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    display: inline-block;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.stamped-button:hover {
    background: linear-gradient(145deg, #d35400, var(--fire-orange));
    box-shadow:
        inset 4px 4px 10px rgba(0, 0, 0, 0.4),
        inset -2px -2px 5px rgba(255, 255, 255, 0.1),
        2px 2px 8px rgba(0, 0, 0, 0.3);
    transform: translateY(2px);
}

.stamped-button:active {
    box-shadow:
        inset 6px 6px 15px rgba(0, 0, 0, 0.5),
        inset -2px -2px 5px rgba(255, 255, 255, 0.1);
    transform: translateY(3px);
}

.cta-button {
    background: linear-gradient(145deg, var(--fire-orange), #d35400);
    color: var(--light-text);
    padding: 18px 36px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    border-radius: 12px;
    box-shadow:
        inset 2px 2px 5px rgba(255, 255, 255, 0.2),
        inset -3px -3px 8px rgba(0, 0, 0, 0.3),
        4px 4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    display: inline-block;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.cta-button:hover {
    background: linear-gradient(145deg, #d35400, var(--fire-orange));
    box-shadow:
        inset 4px 4px 10px rgba(0, 0, 0, 0.4),
        inset -2px -2px 5px rgba(255, 255, 255, 0.1),
        2px 2px 8px rgba(0, 0, 0, 0.3);
    transform: translateY(2px);
}

/* Services Section */
.services-section {
    background: linear-gradient(135deg, var(--medium-grey) 0%, var(--dark-grey) 100%);
    position: relative;
}

.services-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 40%, rgba(225, 112, 85, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(225, 112, 85, 0.03) 0%, transparent 50%);
    z-index: 1;
}

.services-section > * {
    position: relative;
    z-index: 2;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.service-card {
    background: linear-gradient(145deg, var(--light-grey), var(--very-light-grey));
    border: 2px solid var(--fire-orange);
    padding: 40px 25px;
    border-radius: 15px;
    box-shadow:
        inset 2px 2px 5px rgba(255, 255, 255, 0.3),
        inset -2px -2px 5px rgba(0, 0, 0, 0.1),
        4px 4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(145deg, transparent, rgba(225, 112, 85, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.service-card:hover::before {
    opacity: 1;
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow:
        inset 3px 3px 8px rgba(0, 0, 0, 0.2),
        inset -1px -1px 3px rgba(255, 255, 255, 0.2),
        6px 6px 20px rgba(0, 0, 0, 0.3);
    background: linear-gradient(145deg, var(--fire-orange), var(--fire-orange-light));
}

.service-card h3 {
    color: var(--fire-orange);
    margin-bottom: 20px;
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.service-card p {
    color: var(--dark-grey);
    position: relative;
    z-index: 2;
    line-height: 1.6;
}

.service-card:hover h3 {
    color: var(--light-text);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.service-card:hover p {
    color: var(--light-text);
}

/* Work Section */
.work-section {
    background: linear-gradient(135deg, var(--dark-grey) 0%, var(--medium-grey) 100%);
    position: relative;
}

.work-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 60% 30%, rgba(225, 112, 85, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 20% 70%, rgba(225, 112, 85, 0.05) 0%, transparent 50%);
    z-index: 1;
}

.work-section > * {
    position: relative;
    z-index: 2;
}

.work-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-top: 50px;
}

.work-item {
    border-radius: 15px;
    overflow: hidden;
    box-shadow:
        4px 4px 15px rgba(0, 0, 0, 0.3),
        inset 1px 1px 3px rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    background: linear-gradient(145deg, var(--light-grey), var(--very-light-grey));
    padding: 3px;
}

.work-item:hover {
    transform: translateY(-5px);
    box-shadow:
        6px 6px 20px rgba(0, 0, 0, 0.4),
        inset 2px 2px 5px rgba(255, 255, 255, 0.1);
}

.work-placeholder {
    width: 100%;
    height: 250px;
    background: linear-gradient(145deg, var(--fire-orange), var(--fire-orange-light));
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--light-text);
    font-weight: 700;
    font-size: 1.2rem;
    border-radius: 12px;
    transition: all 0.3s ease;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.work-item:hover .work-placeholder {
    background: linear-gradient(145deg, var(--fire-orange-dark), var(--fire-orange));
    transform: scale(1.02);
}

/* About Section */
.about-section {
    background: linear-gradient(135deg, var(--light-grey) 0%, var(--medium-grey) 100%);
    position: relative;
}

.about-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 40% 60%, rgba(74, 74, 74, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(225, 112, 85, 0.03) 0%, transparent 50%);
    z-index: 1;
}

.about-section > * {
    position: relative;
    z-index: 2;
}

.about-section h2 {
    color: var(--light-text);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(176, 176, 176, 0.2));
    padding: 40px;
    border-radius: 20px;
    box-shadow:
        inset 2px 2px 5px rgba(255, 255, 255, 0.3),
        inset -2px -2px 5px rgba(0, 0, 0, 0.1),
        4px 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--fire-orange);
}

.about-content p {
    color: var(--light-text);
    line-height: 1.8;
    font-size: 1.1rem;
}

/* Contact Section */
.contact-section {
    background: linear-gradient(135deg, var(--medium-grey) 0%, var(--dark-grey) 100%);
    padding: 80px 5%;
    position: relative;
}

.contact-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 70%, rgba(225, 112, 85, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(225, 112, 85, 0.03) 0%, transparent 50%);
    z-index: 1;
}

.contact-section > * {
    position: relative;
    z-index: 2;
}

.contact-section h2 {
    text-align: center;
    color: var(--light-text);
    margin-bottom: 60px;
    font-size: 2.5rem;
}

.contact-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    max-width: 1200px;
    margin: 0 auto;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.contact-card {
    background: linear-gradient(145deg, var(--light-grey), var(--very-light-grey));
    padding: 30px;
    border-radius: 15px;
    box-shadow:
        inset 2px 2px 5px rgba(255, 255, 255, 0.3),
        inset -2px -2px 5px rgba(0, 0, 0, 0.1),
        4px 4px 15px rgba(0, 0, 0, 0.2);
    border: 2px solid var(--fire-orange);
    transition: all 0.3s ease;
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow:
        inset 3px 3px 8px rgba(0, 0, 0, 0.1),
        inset -1px -1px 3px rgba(255, 255, 255, 0.2),
        6px 6px 20px rgba(0, 0, 0, 0.3);
}

.contact-card h3 {
    color: var(--fire-orange);
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.contact-card p {
    color: var(--dark-grey);
    font-size: 1.1rem;
    line-height: 1.6;
}

.contact-form {
    background: linear-gradient(145deg, var(--light-grey), var(--very-light-grey));
    padding: 40px;
    border-radius: 20px;
    box-shadow:
        inset 2px 2px 5px rgba(255, 255, 255, 0.3),
        inset -2px -2px 5px rgba(0, 0, 0, 0.1),
        4px 4px 15px rgba(0, 0, 0, 0.2);
    border: 2px solid var(--fire-orange);
}

.contact-form h3 {
    color: var(--fire-orange);
    margin-bottom: 30px;
    font-size: 1.5rem;
    text-align: center;
}

.form-group {
    margin-bottom: 20px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 15px;
    border: 2px solid var(--medium-grey);
    border-radius: 8px;
    background: linear-gradient(145deg, var(--very-light-grey), var(--light-grey));
    color: var(--dark-grey);
    font-size: 1rem;
    font-family: 'Roboto', sans-serif;
    box-shadow:
        inset 2px 2px 5px rgba(0, 0, 0, 0.1),
        inset -1px -1px 3px rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--fire-orange);
    box-shadow:
        inset 3px 3px 8px rgba(0, 0, 0, 0.15),
        inset -1px -1px 3px rgba(255, 255, 255, 0.2),
        0 0 0 3px rgba(225, 112, 85, 0.2);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--medium-grey);
}

.submit-button {
    width: 100%;
    margin-top: 20px;
}

/* Footer */
.main-footer {
    background: linear-gradient(135deg, var(--dark-grey) 0%, var(--medium-grey) 100%);
    text-align: center;
    padding: 40px 5%;
    position: relative;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.footer-logo-image {
    height: 60px;
    width: auto;
    filter: drop-shadow(2px 2px 4px var(--shadow-dark));
}

.copyright {
    font-size: 0.9rem;
    color: var(--light-grey);
    border-top: 1px solid var(--fire-orange);
    padding-top: 20px;
    width: 100%;
}


/* Responsive Design for Mobile */
@media (max-width: 768px) {
    .nav-links {
        display: none; /* For a real site, implement a hamburger menu here */
    }

    .logo-image {
        height: 60px;
    }

    .hero-content {
        bottom: 40px;
        left: 50%;
        transform: translateX(-50%);
        text-align: center;
        width: 90%;
    }

    .hero-content p {
        font-size: 1.1rem;
    }

    .stamped-button, .cta-button {
        padding: 15px 25px;
        font-size: 1rem;
    }

    .nav-button {
        padding: 10px 15px;
        font-size: 0.9rem;
    }

    section {
        padding: 60px 5%;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .work-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .about-content {
        padding: 30px 20px;
    }

    .contact-container {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .contact-section h2 {
        font-size: 2rem;
    }

    .contact-form {
        padding: 30px 20px;
    }

    .contact-info {
        gap: 20px;
    }

    .contact-card {
        padding: 20px;
    }
}