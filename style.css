/* CSS Variables for our color palette */
:root {
    --dark-metal: #2d3436;
    --dark-metal-rgb: 45, 52, 54;
    --silver-gray: #dfe6e9;
    --fire-orange: #e17055;
    --light-text: #ffffff;
}

/* Basic Reset & Body Styling */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Roboto', sans-serif;
    background-color: var(--dark-metal);
    color: var(--silver-gray);
    line-height: 1.6;
}

h1, h2, h3 {
    font-weight: 700;
    color: var(--light-text);
    margin-bottom: 20px;
}

section {
    padding: 80px 5%;
    text-align: center;
}

/* Header and Navigation */
.main-header {
    background-color: rgba(var(--dark-metal-rgb), 0.9);
    backdrop-filter: blur(10px);
    padding: 15px 5%;
    position: sticky;
    top: 0;
    z-index: 100;
    width: 100%;
}

.main-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.8em;
    font-weight: 700;
    color: var(--fire-orange);
    text-decoration: none;
}

.nav-links {
    list-style: none;
    display: flex;
}

.nav-links li {
    margin-left: 30px;
}

.nav-links a {
    text-decoration: none;
    color: var(--silver-gray);
    font-weight: 700;
    transition: color 0.3s ease;
}

.nav-links a:hover {
    color: var(--fire-orange);
}

/* Hero Section */
.hero {
    position: relative;
    height: 90vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0;
    overflow: hidden;
}

.video-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.hero-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero::after { /* Dark overlay for text readability */
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
}

.hero-content {
    z-index: 1;
    text-align: center;
    color: var(--light-text);
}

.hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 10px;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
}

.cta-button {
    background-color: var(--fire-orange);
    color: var(--light-text);
    padding: 15px 30px;
    text-decoration: none;
    font-weight: 700;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.cta-button:hover {
    background-color: #d35400;
}

/* Services Section */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.service-card {
    background-color: rgba(var(--dark-metal-rgb), 0.5);
    border: 1px solid var(--silver-gray);
    padding: 40px 20px;
    border-radius: 5px;
    transition: transform 0.3s ease, background-color 0.3s ease;
}

.service-card:hover {
    transform: translateY(-10px);
    background-color: var(--fire-orange);
}

.service-card h3 {
    color: var(--fire-orange);
    margin-bottom: 15px;
    transition: color 0.3s ease;
}

.service-card:hover h3 {
    color: var(--light-text);
}

/* Work Section */
.work-section {
    background-color: #1e2426;
}

.work-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
    margin-top: 40px;
}

.work-item img {
    width: 100%;
    height: auto;
    display: block;
    border-radius: 5px;
    transition: transform 0.3s ease;
}

.work-item img:hover {
    transform: scale(1.05);
}

/* About Section */
.about-content {
    max-width: 800px;
    margin: 0 auto;
}

/* Footer */
.main-footer {
    background-color: #1e2426;
    text-align: center;
    padding: 40px 5%;
}

.main-footer h3 {
    color: var(--fire-orange);
    font-size: 1.5rem;
}

.footer-contact {
    margin: 20px 0;
}

.copyright {
    font-size: 0.9rem;
    color: #636e72;
}


/* Responsive Design for Mobile */
@media (max-width: 768px) {
    .nav-links {
        display: none; /* For a real site, implement a hamburger menu here */
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    section {
        padding: 60px 5%;
    }
}